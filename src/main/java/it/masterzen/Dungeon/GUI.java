package it.masterzen.Dungeon;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import it.masterzen.commands.Main;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.sql.Array;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public class GUI implements Listener {

    private it.masterzen.Dungeon.Main main;
    private it.masterzen.Withdraw.Main withdrawManager;
    private final String prefix = "§e§lDUNGEON §8»§7 ";

    public GUI(it.masterzen.Dungeon.Main main) {
        this.main = main;
        withdrawManager = new it.masterzen.Withdraw.Main(main.mainClass);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();
            boolean openMainMenu = false;

            if (event.getView().getTitle().equalsIgnoreCase("§e§lDUNGEON §f| §7Shop")) {
                event.setCancelled(true);
                if (event.getSlot() == 11) {
                    // Custom Wheat Seed
                    if (event.isLeftClick()) {
                        giveCustomSeed(player, "wheat", 1, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveCustomSeed(player, "wheat", 1, true);
                        openMainMenu = true;
                    }
                } else if (event.getSlot() == 13) {
                    // Custom Carrot Seed
                    if (event.isLeftClick()) {
                        giveCustomSeed(player, "carrot", 1, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveCustomSeed(player, "carrot", 1, true);
                        openMainMenu = true;
                    }
                } else if (event.getSlot() == 15) {
                    // Custom Potato Seed
                    if (event.isLeftClick()) {
                        giveCustomSeed(player, "potato", 1, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveCustomSeed(player, "potato", 1, true);
                        openMainMenu = true;
                    }
                } /*else if (event.getSlot() == 15) {
                    // Custom Wheat Seed (5x)
                    if (event.isLeftClick()) {
                        giveCustomSeed(player, "wheat", 5, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveCustomSeed(player, "wheat", 5, true);
                        openMainMenu = true;
                    }
                } else if (event.getSlot() == 16) {
                    // Custom Carrot Seed (5x)
                    if (event.isLeftClick()) {
                        giveCustomSeed(player, "carrot", 5, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveCustomSeed(player, "carrot", 5, true);
                        openMainMenu = true;
                    }
                }*/ else if (event.getSlot() == 22) {
                    if (player.getInventory().getItemInMainHand() != null && player.getInventory().getItemInMainHand().getType().equals(Material.DIAMOND_PICKAXE)) {
                        openEnchantGUI(player);
                    } else {
                        player.sendMessage(prefix + "§cPlease hold your pickaxe");
                    }
                    /*if (event.isLeftClick()) {
                        giveXP(player, false, 5000, 1);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 5000, 1);
                        openMainMenu = true;
                    }*/
                } /*else if (event.getSlot() == 13) {
                    if (event.isLeftClick()) {
                        giveXP(player, false, 25000, 2);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 25000, 2);
                        openMainMenu = true;
                    }
                } else if (event.getSlot() == 15) {
                    if (event.isLeftClick()) {
                        giveXP(player, false, 100000, 3);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 100000, 3);
                        openMainMenu = true;
                    }
                } else if (event.getSlot() == 22) {
                    if (player.getInventory().getItemInMainHand() != null && player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                        openEnchantGUI(player);
                    } else {
                        player.sendMessage(prefix + "Please hold the pickaxe to enchant it");
                    }
                }*/

                if (openMainMenu) {
                    openGUI(player);
                }
            }
        }
    }

    public void giveXP(Player player, boolean max, int amount, int tier) {
        long playerBalance = main.getPoints(player, tier);
        long price = 0;
        if (tier == 1) {
            price = 25;
        } else if (tier == 2 || tier == 3) {
            price = 10;
        }

        if (playerBalance >= price || player.isOp()) {
            if (max) {
                long totalXP = 0;
                int iterations = 0;

                while (playerBalance >= price) {
                    totalXP = totalXP + amount; // 50k to 100k
                    playerBalance = playerBalance - price;
                    iterations++;
                }

                withdrawManager.giveItem(player, "XP", totalXP);
                main.removePoints(player, tier, iterations * price);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalXP));
            } else {
                withdrawManager.giveItem(player, "XP", amount);
                main.removePoints(player, tier, price);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(amount));
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Crystals");
        }
    }

    public void giveRandomSpawner(Player player, boolean maxUse) {
        PlayerData data = AlphaBlockBreak.GetInstance().getMongoReader().getPlayerData(player.getUniqueId());
        int price = 250;
        if (data.getDungeonDiscount() != null) {
            price = 250 - data.getDungeonDiscount();
        }

        if (main.getPoints(player, 1) >= price) {
            int iterations = 1;
            if (maxUse) {
                iterations = 9999;
            }
            Map<String, Integer> spawnersToAdd = new HashMap<>();
            while (main.getPoints(player, 1) >= price && iterations > 0) {
                int chance = ThreadLocalRandom.current().nextInt(4);

                if (chance == 0) {
                    if (spawnersToAdd.containsKey("Silverfish")) {
                        spawnersToAdd.put("Silverfish", spawnersToAdd.get("Silverfish") + 1);
                    } else {
                        spawnersToAdd.put("Silverfish", 1);
                    }
                } else if (chance == 1) {
                    if (spawnersToAdd.containsKey("Villager")) {
                        spawnersToAdd.put("Villager", spawnersToAdd.get("Villager") + 1);
                    } else {
                        spawnersToAdd.put("Villager", 1);
                    }
                } else if (chance == 2) {
                    if (spawnersToAdd.containsKey("Bat")) {
                        spawnersToAdd.put("Bat", spawnersToAdd.get("Bat") + 1);
                    } else {
                        spawnersToAdd.put("Bat", 1);
                    }
                } else {
                    if (spawnersToAdd.containsKey("Parrot")) {
                        spawnersToAdd.put("Parrot", spawnersToAdd.get("Parrot") + 1);
                    } else {
                        spawnersToAdd.put("Parrot", 1);
                    }
                }

                main.removePoints(player, 1, price);
                iterations--;
            }

            for (String spawner : spawnersToAdd.keySet()) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner " + spawner + " " + spawnersToAdd.get(spawner));
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveVillagerSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 3) >= 50) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 3) >= 50 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 3, 50);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Villager " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aVillager §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveMoneyNoteSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 1) >= 100) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 1) >= 100 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 1, 100);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Bat " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aMoney Note §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveTokenNoteSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 2) >= 75) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 2) >= 75 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 2, 75);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Parrot " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aToken Note §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveSilverfishSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 1) >= 100) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 1) >= 100 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 1, 100);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Silverfish " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aSilverfish §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void openEnchantGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lENCHANT §f| §7Menu");
        Main.FillBorder(gui);

        List<String> pickaxelore = player.getInventory().getItemInMainHand().getItemMeta().getLore();
        ItemStack fortune = main.mainClass.getEnchantManager().getSlotItem("Dungeon Fortune", pickaxelore, player);
        ItemStack luck = main.mainClass.getEnchantManager().getSlotItem("Dungeon Luck", pickaxelore, player);
        ItemStack killer = main.mainClass.getEnchantManager().getSlotItem("Dungeon Killer", pickaxelore, player);
        ItemStack bomb = main.mainClass.getEnchantManager().getSlotItem("Dungeon Bomb", pickaxelore, player);
        ItemStack luckybeacon = main.mainClass.getEnchantManager().getSlotItem("Lucky Beacon", pickaxelore, player);

        gui.setItem(11, killer);
        gui.setItem(12, fortune);
        gui.setItem(13, luck);
        gui.setItem(14, bomb);
        gui.setItem(15, luckybeacon);

        player.openInventory(gui);
    }

    /**
     * Gives custom crop seeds to the player
     */
    public void giveCustomSeed(Player player, String cropType, int quantity, boolean maxUse) {
        int price = getSeedPrice(cropType, quantity);
        int tier = getSeedTier(cropType);

        PlayerData data = AlphaBlockBreak.GetInstance().getMongoReader().getPlayerData(player.getUniqueId());
        if (data.getDungeonDiscount() != null && tier == 1) {
            price = price - data.getDungeonDiscount();
        }

        if (main.getPoints(player, tier) >= price) {
            int iterations = 1;
            int seedsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }

            for (int i = 0; i < iterations; i++) {
                if (main.getPoints(player, tier) >= price) {
                    main.removePoints(player, price, tier);
                    seedsToAdd += quantity;
                } else {
                    break;
                }
            }

            if (seedsToAdd > 0) {
                ItemStack seeds = createCustomSeed(cropType, seedsToAdd);
                if (seeds != null) {
                    player.getInventory().addItem(seeds);
                    player.sendMessage(prefix + "You bought §a§l" + seedsToAdd + " §7custom " + cropType + " seeds!");
                }
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals!");
        }
    }

    /**
     * Gets the price for custom seeds
     */
    private int getSeedPrice(String cropType, int quantity) {
        int basePrice;
        switch (cropType.toLowerCase()) {
            case "wheat":
                basePrice = 100; // Fortune levels reward
                break;
            case "carrot":
                basePrice = 150; // Token Greed levels reward
                break;
            case "potato":
                basePrice = 200; // Money/Token boost reward
                break;
            default:
                basePrice = 100;
        }
        return basePrice * quantity;
    }

    /**
     * Gets the tier for custom seeds
     */
    private int getSeedTier(String cropType) {
        switch (cropType.toLowerCase()) {
            case "wheat":
                return 1; // Tier 1 crystals
            case "carrot":
                return 2; // Tier 2 crystals
            case "potato":
                return 3; // Tier 3 crystals
            default:
                return 1;
        }
    }

    /**
     * Creates a custom seed item
     */
    private ItemStack createCustomSeed(String cropType, int quantity) {
        Material seedMaterial;
        String displayName;
        List<String> lore = new ArrayList<>();

        switch (cropType.toLowerCase()) {
            case "wheat":
                seedMaterial = XMaterial.WHEAT_SEEDS.parseMaterial();
                displayName = "§6§lCUSTOM WHEAT §7Seed";
                lore.add("§7Plant on your island to grow");
                lore.add("§7custom wheat that gives");
                lore.add("§a§l10-50 Fortune levels §7when harvested!");
                break;
            case "carrot":
                seedMaterial = XMaterial.CARROT.parseMaterial();
                displayName = "§6§lCUSTOM CARROT §7Seed";
                lore.add("§7Plant on your island to grow");
                lore.add("§7custom carrots that give");
                lore.add("§a§l10-25 Token Greed levels §7when harvested!");
                break;
            case "potato":
                seedMaterial = XMaterial.POTATO.parseMaterial();
                displayName = "§6§lCUSTOM POTATO §7Seed";
                lore.add("§7Plant on your island to grow");
                lore.add("§7custom potatoes that give");
                lore.add("§a§l5% Money OR Token Boost §7when harvested!");
                break;
            default:
                return null;
        }

        ItemStack seed = new ItemStack(seedMaterial, quantity);
        ItemMeta meta = seed.getItemMeta();
        meta.setDisplayName(displayName);
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        meta.setLore(lore);
        seed.setItemMeta(meta);

        return seed;
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lDUNGEON §f| §7Shop");
        Main.FillBorder(gui);

        /*ItemStack xpT1 = new ItemStack(Material.EXP_BOTTLE);
        ItemMeta meta = xpT1.getItemMeta();
        meta.setDisplayName("§a5k XP");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§3§lPRICE");
        lore.add("  §b25 §7Tier 1 Crystals");
        lore.add("");
        lore.add("§7Left click: 1x");
        lore.add("§7Right click: Max Use");
        meta.setlore(lore);
        xpT1.setItemMeta(meta);

        ItemStack xpT2 = new ItemStack(Material.EXP_BOTTLE);
        meta = xpT2.getItemMeta();
        meta.setDisplayName("§a25k XP");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§3§lPRICE");
        lore.add("  §b10 §7Tier 2 Crystals");
        lore.add("");
        lore.add("§7Left click: 1x");
        lore.add("§7Right click: Max Use");
        meta.setlore(lore);
        xpT2.setItemMeta(meta);

        ItemStack xpT3 = new ItemStack(Material.EXP_BOTTLE);
        meta = xpT3.getItemMeta();
        meta.setDisplayName("§a100k XP");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§3§lPRICE");
        lore.add("  §b10 §7Tier 3 Crystals");
        lore.add("");
        lore.add("§7Left click: 1x");
        lore.add("§7Right click: Max Use");
        meta.setlore(lore);
        xpT3.setItemMeta(meta);

        ItemStack enchantMenu = new ItemStack(Material.BOOK);
        meta = enchantMenu.getItemMeta();
        meta.setDisplayName("§aDungeon Enchant §7| Menu");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Click me to open the enchant shop");
        meta.setlore(lore);
        enchantMenu.setItemMeta(meta);

        gui.setItem(11, xpT1);
        gui.setItem(13, xpT2);
        gui.setItem(15, xpT3);
        gui.setItem(22, enchantMenu);*/

        PlayerData data = AlphaBlockBreak.GetInstance().getMongoReader().getPlayerData(player.getUniqueId());

        // Custom Wheat Seed (1x)
        int wheatPrice = getSeedPrice("wheat", 1);
        int wheatDiscount = 0;
        if (data.getDungeonDiscount() != null) {
            wheatPrice = wheatPrice - data.getDungeonDiscount();
            wheatDiscount = data.getDungeonDiscount();
        }
        ItemStack wheatSeed = XMaterial.WHEAT_SEEDS.parseItem();
        assert wheatSeed != null;
        ItemMeta meta = wheatSeed.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM WHEAT §7Seed");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e" + wheatPrice + " §fTier 1" + (wheatDiscount > 0 ? " §7§o(-" + wheatDiscount + ")" : ""));
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 1) + " §fTier 1");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §a§l10-50 Fortune levels §7when harvested");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        meta.setLore(lore);
        wheatSeed.setItemMeta(meta);

        // Custom Carrot Seed (1x)
        int carrotPrice = getSeedPrice("carrot", 1);
        ItemStack carrotSeed = XMaterial.CARROT.parseItem();
        assert carrotSeed != null;
        meta = carrotSeed.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM CARROT §7Seed");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e" + carrotPrice + " §fTier 2");
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 2) + " §fTier 2");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §a§l10-25 Token Greed levels §7when harvested");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        meta.setLore(lore);
        carrotSeed.setItemMeta(meta);

        // Custom Potato Seed (1x)
        int potatoPrice = getSeedPrice("potato", 1);
        ItemStack potatoSeed = XMaterial.POTATO.parseItem();
        assert potatoSeed != null;
        meta = potatoSeed.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM POTATO §7Seed");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e" + potatoPrice + " §fTier 3");
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 3) + " §fTier 3");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §a§l5% Money OR Token Boost §7when harvested");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        meta.setLore(lore);
        potatoSeed.setItemMeta(meta);

        /*// Custom Wheat Seed (5x)
        int wheatPrice5x = getSeedPrice("wheat", 5);
        int wheatDiscount5x = 0;
        if (data.getDungeonDiscount() != null) {
            wheatPrice5x = wheatPrice5x - (data.getDungeonDiscount() * 5);
            wheatDiscount5x = data.getDungeonDiscount() * 5;
        }
        ItemStack wheatSeed5x = XMaterial.WHEAT_SEEDS.parseItem();
        assert wheatSeed5x != null;
        wheatSeed5x.setAmount(5);
        meta = wheatSeed5x.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM WHEAT §7Seeds §e(5x)");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e" + wheatPrice5x + " §fTier 1" + (wheatDiscount5x > 0 ? " §7§o(-" + wheatDiscount5x + ")" : ""));
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 1) + " §fTier 1");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §a§l10-50 Fortune levels §7when harvested");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 5x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        meta.setLore(lore);
        wheatSeed5x.setItemMeta(meta);

        // Custom Carrot Seed (5x)
        int carrotPrice5x = getSeedPrice("carrot", 5);
        ItemStack carrotSeed5x = XMaterial.CARROT.parseItem();
        assert carrotSeed5x != null;
        carrotSeed5x.setAmount(5);
        meta = carrotSeed5x.getItemMeta();
        meta.setDisplayName("§6§lCUSTOM CARROT §7Seeds §e(5x)");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e" + carrotPrice5x + " §fTier 2");
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 2) + " §fTier 2");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §a§l10-25 Token Greed levels §7when harvested");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 5x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        lore.add("§c§lWARNING: §7Can only be planted on your island!");
        meta.setLore(lore);
        carrotSeed5x.setItemMeta(meta);*/

        // Keep the enchant menu as is - it's not a spawner

        ItemStack enchantMenu = new ItemStack(Material.BOOK);
        meta = enchantMenu.getItemMeta();
        meta.setDisplayName("§aDungeon Enchant §7| Menu");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Click me to open the enchant shop");
        meta.setLore(lore);
        enchantMenu.setItemMeta(meta);

        gui.setItem(11, wheatSeed);
        gui.setItem(13, carrotSeed);
        gui.setItem(15, potatoSeed);
        /*gui.setItem(15, wheatSeed5x);
        gui.setItem(16, carrotSeed5x);*/
        gui.setItem(22, enchantMenu);

        player.openInventory(gui);
    }
}
