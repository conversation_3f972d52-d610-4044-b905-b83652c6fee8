package it.masterzen.MongoDB;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.model.CreateCollectionOptions;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.InsertManyOptions;
import com.mongodb.client.model.Updates;
import it.masterzen.MongoDB.DataTypes.CropGeneratorPojo;
import it.masterzen.MongoDB.DataTypes.LogPojo;
import it.masterzen.MongoDB.DataTypes.MilestonePojo;
import it.masterzen.MongoDB.DataTypes.PlayerBoosters;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.CropsGenerator;
import it.masterzen.blockbreak.Milestones;
import me.clip.ezblocks.EZBlocks;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;

import java.security.InvalidParameterException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.mongodb.client.model.Filters.eq;

public class Main {

    private final String prefix = "§e§lMONGO §8»§7 ";
    public final AlphaBlockBreak mainClass;

    private static ObjectMapper mapper;
    private static MongoClient mongoClient;
    private static MongoDatabase mongoDatabase;
    private Map<String, PlayerData> playerDataMap = new ConcurrentHashMap<>();
    private Map<String, IslandData> islandDataMap = new ConcurrentHashMap<>();
    private Map<Player, PlayerBoosters> playerBoostersMap = new HashMap<>();
    private ServerData serverData;

    public Main(AlphaBlockBreak plugin) {
        mainClass = plugin;

        mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleModule module = new SimpleModule();
        module.addSerializer(Date.class, new DateJsonSerializer());
        module.addDeserializer(Date.class, new DateJsonDeserializer());
        module.addSerializer(ObjectId.class, new ObjectIdJsonSerializer());
        module.addDeserializer(ObjectId.class, new ObjectIdJsonDeserializer());
        mapper.registerModule(module);

        loadDatabase();
        //loadPlayersData();
    }

    public Map<String, PlayerData> getPlayerDataMap() { return playerDataMap; }

    public void closeConnection() {
        for (PlayerData data : playerDataMap.values()) {
            if (Bukkit.getPlayer(UUID.fromString(data.getUuid())) != null) {
                savePlayerData(data, false);
            }
        }

        if (mongoClient != null) {
            mongoClient.close();
        }
    }

    public void loadDatabase() {
        MongoClientOptions options = MongoClientOptions.builder()
            .serverSelectionTimeout(1000)
            .connectTimeout(10000)
            .socketTimeout(60000)
            .build();

        mongoClient = new MongoClient("localhost:27017", options);
        mongoDatabase = mongoClient.getDatabase("alphaprison");

        List<String> list = new ArrayList<>();
        if (!mongoDatabase.listCollectionNames().into(list).contains("players")) {
            mongoDatabase.createCollection("players");
            mainClass.getLogger().info("Collection 'players' Created");
        }

        /*MongoIterable<String> collectionList = mongoDatabase.listCollectionNames();
        boolean collectionExists = false;
        for (String collectionName : collectionList) {
            if (StringUtils.equals(collectionName, "players")) {
                collectionExists = true;
                break;
            }
        }
        if (!collectionExists) {
            mongoDatabase.createCollection("players");
        }*/
        mainClass.getLogger().info("Database Loaded");
    }

    /*public PlayerData getPlayerData(Player player) {
        if (!playerDataMap.containsKey(player.getUniqueId().toString())) {
            loadPlayerData(player.getUniqueId());
        }

        return playerDataMap.get(player.getUniqueId().toString());
    }*/

    public void loadPlayerBoosters(Player player) {
        PlayerBoosters booster = getPlayerBoosters(player);
        if (booster == null) {
            booster = new PlayerBoosters();
            booster.setMoney(0D);
            booster.setTokens(0D);

            PlayerData data = getPlayerData(player.getUniqueId());
            if (data.getKeysMoneyBooster() != null) {
                booster.setMoney(data.getKeysMoneyBooster());
            }
            if (data.getKeysTokenBooster() != null) {
                booster.setTokens(data.getKeysTokenBooster());
            }
            updatePlayerBoosters(player, booster);
        }
    }

    public PlayerBoosters getPlayerBoosters(Player player) {
        return playerBoostersMap.get(player);
    }

    public void updatePlayerBoosters(Player player, PlayerBoosters playerBoosters) {
        playerBoostersMap.put(player, playerBoosters);
    }

    public void setPlayerMoneyBoosterInMap(Player player, Double amount) {
        PlayerBoosters boosters = playerBoostersMap.get(player);
        if (boosters == null) {
            boosters = new PlayerBoosters();
        }
        boosters.setMoney(amount);

        playerBoostersMap.put(player, boosters);
    }

    public void setPlayerTokenBoosterInMap(Player player, Double amount) {
        PlayerBoosters boosters = playerBoostersMap.get(player);
        if (boosters == null) {
            boosters = new PlayerBoosters();
        }
        boosters.setTokens(amount);

        playerBoostersMap.put(player, boosters);
    }

    public PlayerData getPlayerData(UUID playerUUID) {
        if (!playerDataMap.containsKey(playerUUID.toString())) {
            loadPlayerData(playerUUID);
        }

        return playerDataMap.get(playerUUID.toString());
    }

    public IslandData getIslandData(UUID islandUUID) {
        if (!islandDataMap.containsKey(islandUUID.toString())) {
            loadIslandData(islandUUID);
        }

        return islandDataMap.get(islandUUID.toString());
    }

    public void loadPlayersData() {
        /*MongoCollection<Document> playerDataList = mongoDatabase.getCollection("players");

        FindIterable<Document> list = playerDataList.find();
        List<PlayerData> playerData = fromDocumentList(list, PlayerData.class);
        for (PlayerData data : playerData) {
            playerDataMap.put(data.getUuid(), data);
        }*/

        for (Player player : Bukkit.getOnlinePlayers()) {
            loadPlayerData(player.getUniqueId());
        }
    }

    //public void loadPlayerData(Player player) {
    //    //if (!playerDataMap.containsKey(player.getUniqueId().toString())) {
    //    MongoCollection<Document> playerDataList = mongoDatabase.getCollection("players");
    //    //DateFormat format = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
    //    Document playerData = playerDataList
    //            .find(Filters.and(eq("uuid", player.getUniqueId().toString()))).first();
//
    //    if (playerData != null) {
    //        playerDataMap.put(player.getUniqueId().toString(), fromDocument(playerData, PlayerData.class));
    //        alignMilestones(player.getUniqueId(), false);
    //    } else {
    //        PlayerData data = new PlayerData();
    //        data.setUuid(player.getUniqueId().toString());
    //        playerDataMap.put(player.getUniqueId().toString(), data);
    //    }
    //    //}
    //}

    public void loadPlayerData(UUID playerUUID) {
        //if (!playerDataMap.containsKey(playerUUID.toString())) {
        MongoCollection<Document> playerDataList = mongoDatabase.getCollection("players");
        //DateFormat format = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Document playerData = playerDataList.find(eq("uuid", playerUUID.toString())).first();

        if (playerData != null) {
            playerDataMap.put(playerUUID.toString(), fromDocument(playerData, PlayerData.class));
            alignMilestones(playerUUID, false);
        } else {
            PlayerData data = new PlayerData();
            data.setUuid(playerUUID.toString());
            data.setFirstJoinDate(new Date());
            playerDataMap.put(playerUUID.toString(), data);
        }

        // Logging
        /*if (Bukkit.getPlayer(UUID.fromString(playerDataMap.get(playerUUID.toString()).getUuid())) != null) {
            PlayerData data = playerDataMap.get(playerUUID.toString());
            Player player = Bukkit.getPlayer(UUID.fromString(data.getUuid()));
            Map<Object, Object> datas = new LinkedHashMap<>();
            datas.put("rawBlocks", data.getRawBlocks());
            datas.put("blocksMined", data.getBlocksMined());
            datas.put("ezBlocks", EZBlocks.getEZBlocks().getBlocksBroken(Bukkit.getPlayer(UUID.fromString(data.getUuid()))));
            datas.put("schedulerReason", "data loading"); // false = playerLeft
            saveLog(player, datas, UUID.fromString(data.getUuid()));
        }*/

        // DEBUG 09/10
        /*if (playerDataMap.get(playerUUID.toString()).getPickaxeCurrentXP() != null && playerDataMap.get(playerUUID.toString()).getPickaxeCurrentXP() == 0) {
            Bukkit.getLogger().warning("(!) (!) Pickaxe XP to 0 for Player " + Bukkit.getOfflinePlayer(playerUUID).getName() + " (Loading)");
        }
        if (playerDataMap.get(playerUUID.toString()).getBlocksMined() != null && Bukkit.getPlayer(UUID.fromString(playerDataMap.get(playerUUID.toString()).getUuid())) != null &&
                (playerDataMap.get(playerUUID.toString()).getBlocksMined() + 10000) < EZBlocks.getEZBlocks().getBlocksBroken(Bukkit.getPlayer(UUID.fromString(playerDataMap.get(playerUUID.toString()).getUuid())))) {
            Bukkit.getLogger().warning("(!) (!) Blocks Mined possibly bugged for Player " + Bukkit.getOfflinePlayer(playerUUID).getName() + " (Loading)");
        }*/
        //}
    }

    public void loadIslandData(UUID islandUUID) {
        MongoCollection<Document> playerDataList = mongoDatabase.getCollection("islands");
        Document islandData = playerDataList.find(eq("uuid", islandUUID.toString())).first();

        if (islandData != null) {
            islandDataMap.put(islandUUID.toString(), fromDocument(islandData, IslandData.class));
        } else {
            IslandData data = new IslandData();
            data.setUuid(islandUUID.toString());
            islandDataMap.put(islandUUID.toString(), data);
        }
    }

    public void loadServerData() {
        MongoCollection<Document> serverDataList = mongoDatabase.getCollection("server");
        Document foundedServerData = serverDataList.find().first();

        if (foundedServerData != null) {
            serverData = fromDocument(foundedServerData, ServerData.class);
        } else {
            serverData = new ServerData();
        }
    }

    public ServerData getServerData() {
        if (serverData == null) {
            loadServerData();
        }

        return serverData;
    }

    public void savePlayerData(PlayerData data, boolean fromScheduler) {
        if (data == null) {
            mainClass.getLogger().warning("(!) Found empty PlayerData");
            return;
        }

        data.setLastUpdate(new Date());
        if (data.getFirstJoinDate() == null) {
            data.setFirstJoinDate(new Date());
        }
        data.setName(Bukkit.getOfflinePlayer(UUID.fromString(data.getUuid())).getName()); // update player name with the latest one found

        MongoCollection<Document> collection = mongoDatabase.getCollection("players");
        Document playerData = collection.find(eq("uuid", data.getUuid())).first();

        if (playerData != null) {
            PlayerData tmpPlayerData = fromDocument(playerData, PlayerData.class);
            collection.replaceOne(
                    new Document("_id", tmpPlayerData.getId()),
                    toDocument(data));
        } else {
            Document doc = toDocument(data);
            collection.insertOne(doc);
        }

        // Logging
        /*if (Bukkit.getPlayer(UUID.fromString(data.getUuid())) != null) {

            MongoCollection<Document> playerDataList = mongoDatabase.getCollection("players");
            playerData = playerDataList.find(eq("uuid", data.getUuid())).first();

            PlayerData tmpData = fromDocument(playerData, PlayerData.class);

            Player player = Bukkit.getPlayer(UUID.fromString(data.getUuid()));
            Map<Object, Object> datas = new LinkedHashMap<>();
            datas.put("rawBlocks", tmpData.getRawBlocks());
            datas.put("blocksMined", tmpData.getBlocksMined());
            datas.put("ezBlocks", EZBlocks.getEZBlocks().getBlocksBroken(Bukkit.getPlayer(UUID.fromString(data.getUuid()))));
            datas.put("schedulerReason", fromScheduler); // false = playerLeft
            saveLog(player, datas, UUID.fromString(data.getUuid()));
        }*/

        // DEBUG 09/10
        /*if (data.getPickaxeCurrentXP() != null && data.getPickaxeCurrentXP() == 0) {
            Bukkit.getLogger().warning("(!) (!) Pickaxe XP to 0 for Player " + Bukkit.getOfflinePlayer(UUID.fromString(data.getUuid())).getName() + " (Saving)");
        }
        if (data.getBlocksMined() != null && Bukkit.getPlayer(UUID.fromString(data.getUuid())) != null &&
                (data.getBlocksMined() + 10000) < EZBlocks.getEZBlocks().getBlocksBroken(Bukkit.getPlayer(UUID.fromString(data.getUuid())))) {
            Bukkit.getLogger().warning("(!) (!) Blocks Mined possibly bugged for Player " + Bukkit.getOfflinePlayer(UUID.fromString(data.getUuid())).getName() + " (Saving)");
        }*/
    }

    public void saveIslandData(IslandData data) {
        if (data == null) {
            mainClass.getLogger().warning("(!) Found empty IslandData");
            return;
        }

        data.setLastUpdate(new Date());

        MongoCollection<Document> collection = mongoDatabase.getCollection("islands");
        Document islandData = collection.find(Filters.and(eq("uuid", data.getUuid()))).first();

        if (islandData != null) {
            IslandData tmpIslandData = fromDocument(islandData, IslandData.class);
            collection.replaceOne(
                    new Document("_id", tmpIslandData.getId()),
                    toDocument(data));
        } else {
            Document doc = toDocument(data);
            collection.insertOne(doc);
        }
    }

    public void saveServerData() {
        if (serverData == null) {
            loadServerData();
        }

        serverData.setLastUpdate(new Date());

        MongoCollection<Document> collection = mongoDatabase.getCollection("server");
        Document findedServerData = collection
                .find(eq("_id", serverData.getId())).first();

        if (findedServerData != null) {
            collection.replaceOne(
                    new Document("_id", serverData.getId()),
                    toDocument(serverData));
        } else {
            Document doc = toDocument(serverData);
            collection.insertOne(doc);
        }
    }

    public void saveMilestoneRecord(Player player, String randomMilestone) {
        MongoCollection<Document> collection = mongoDatabase.getCollection("milestones");
        Document record = new Document();
        record.put("uuid", player.getUniqueId().toString());
        record.put("creationDate", new Date());
        record.put("milestone", randomMilestone);
        collection.insertOne(record);
    }

    public void saveLog(Player player, Map<Object, Object> datas, UUID playerUUIDFromData) {
        if (player != null) {
            LogPojo log = new LogPojo();
            log.setPlayer(player.getName());
            log.setPlayerUUID(player.getUniqueId().toString());
            log.setPlayerUUIDSaved(playerUUIDFromData.toString());
            log.setDate(new Date());
            log.setDatas(datas);

            MongoCollection<Document> collection = mongoDatabase.getCollection("logs");
            collection.insertOne(toDocument(log));
        }
    }

    public void addCropGenerator(Player player, UUID islandUUID) {
        MongoCollection<Document> collection = mongoDatabase.getCollection("islandGenerators");
        CropGeneratorPojo generator = new CropGeneratorPojo();
        generator.setIslandUUID(islandUUID.toString());
        generator.setPlayerUUID(player.getUniqueId().toString());
        generator.setLastClaimDate(new Date());
        collection.insertOne(toDocument(generator));
    }

    public static CropGeneratorPojo loadGenerator(ObjectId generatorId) {
        if (generatorId == null) {
            throw new InvalidParameterException("empty generatorId");
        }

        MongoCollection<Document> collection = mongoDatabase.getCollection("islandGenerators");
        Document doc = collection.find(eq("_id", generatorId)).first();
        return fromDocument(doc, CropGeneratorPojo.class);
    }

    public void updateCropGenerator(ObjectId generatorId) {
        if (generatorId == null) {
            mainClass.getLogger().warning("(!) Found empty generatorId");
            return;
        }

        CropGeneratorPojo generator = loadGenerator(generatorId);
        generator.setLastClaimDate(new Date());

        MongoCollection<Document> collection = mongoDatabase.getCollection("islandGenerators");
        collection.replaceOne(
                new Document("_id", generator.getId()),
                toDocument(generator)
        );
    }

    public void removeCropGenerator(ObjectId generatorId) {
        if (generatorId == null) {
            mainClass.getLogger().warning("(!) Found empty generatorId");
            return;
        }

        CropGeneratorPojo generator = loadGenerator(generatorId);
        generator.setLastClaimDate(new Date());

        MongoCollection<Document> collection = mongoDatabase.getCollection("islandGenerators");
        collection.deleteOne(eq("_id", generatorId));
    }

    public List<MilestonePojo> getPlayerMilestone(Player player) {
        MongoCollection<Document> collection = mongoDatabase.getCollection("milestones");

        FindIterable<Document> list = collection.find(Filters.and(eq("uuid", player.getUniqueId().toString())));
        return fromDocumentList(list, MilestonePojo.class);
    }

    public List<CropGeneratorPojo> getIslandCropGenerators(UUID islandUUID) {
        MongoCollection<Document> collection = mongoDatabase.getCollection("islandGenerators");

        FindIterable<Document> list = collection.find(Filters.and(eq("islandUUID", islandUUID.toString())));
        return fromDocumentList(list, CropGeneratorPojo.class);
    }

    // TODO: DA MIGLIORARE
    public void saveData() {
        Map<String, PlayerData> tmpPlayerDataMap = new ConcurrentHashMap<>(playerDataMap);
        for (PlayerData data : tmpPlayerDataMap.values()) {
            //Bukkit.broadcastMessage("blocks: " + data.getRawBlocks() + " player: " + data.getName());
            if (Bukkit.getPlayer(UUID.fromString(data.getUuid())) != null) { // if the player is online
                savePlayerData(data, true);
            }
        }

        for (IslandData data : islandDataMap.values()) {
            saveIslandData(data);
        }

        saveServerData();
    }

    public void alignMilestones(UUID playerUUID, boolean messages) {
        if (playerUUID != null) {
            MongoCollection<Document> collection = mongoDatabase.getCollection("milestones");
            FindIterable<Document> list = collection
                    .find(Filters.and(eq("uuid", playerUUID.toString())));
            List<MilestonePojo> milestonePojoList = fromDocumentList(list, MilestonePojo.class);

            List<String> uuidOfPlayerExecuted = new ArrayList<>();
            if (messages) {
                Bukkit.broadcastMessage("Starting Alignment...");
            }
            PlayerData data = getPlayerData(playerUUID);
            if (data != null) {
                for (MilestonePojo milestonePojo : milestonePojoList) {
                    // SE PASSO PLAYER ESEGUO SOLO QUELLO
                    if (!uuidOfPlayerExecuted.contains(milestonePojo.getUuid())) {
                        uuidOfPlayerExecuted.add(milestonePojo.getUuid());
                        data.setKeysBoosterFromMilestone(0D);
                        data.setMoneyBoosterFromMilestone(0D);
                        data.setTokenBoosterFromMilestone(0D);
                        data.setXpBoosterFromMilestone(0D);
                    }

                    if (Milestones.Stones.valueOf(milestonePojo.getMilestone()).equals(Milestones.Stones.MONEY)) {
                        data.addMoneyBooster(1D);
                    } else if (Milestones.Stones.valueOf(milestonePojo.getMilestone()).equals(Milestones.Stones.TOKENS)) {
                        data.addTokenBooster(1D);
                    } else if (Milestones.Stones.valueOf(milestonePojo.getMilestone()).equals(Milestones.Stones.KEYS)) {
                        data.addKeysBooster(1D);
                    } else if (Milestones.Stones.valueOf(milestonePojo.getMilestone()).equals(Milestones.Stones.XP)) {
                        data.addXpBooster(1D);
                    }

                }

                //savePlayerData(data);
                if (messages) {
                    Bukkit.broadcastMessage("Data Aligned Successfully");
                }
            } else {
                mainClass.getLogger().warning("(!) alignMilestones: data == null");
            }
        } else {
            mainClass.getLogger().warning("(!) alignMilestones: playerUUID == null");
        }
    }

    public void fixPickaxeData(Player player) {
        PlayerData data = getPlayerData(player.getUniqueId());
        int ezBlocksCounter = EZBlocks.getEZBlocks().getBlocksBroken(player);

        if (ezBlocksCounter > 0) {
            if (data.getPickaxeCurrentXP() == null || data.getPickaxeCurrentXP() < ezBlocksCounter) {
                data.setPickaxeCurrentXP(ezBlocksCounter);
            }
            if (data.getBlocksMined() == null || data.getBlocksMined() < ezBlocksCounter) {
                data.setBlocksMined(ezBlocksCounter);
            }
            if (data.getRawBlocks() == null || data.getRawBlocks() < ezBlocksCounter) {
                data.setRawBlocks(ezBlocksCounter);
            }
        }
    }

    public static <T> T fromDocument(Document document, Class<T> objectClass) {
        T result = null;
        if (document != null) {
            result = deserializeFromJson(document.toJson(), objectClass);
        }
        return result;
    }

    public static Document toDocument(Object object) {
        Document result = null;
        if (object != null) {
            result = Document.parse(serializeToJson(object));
        }
        return result;
    }

    public static String serializeToJson(Object object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (Exception ex) {
            // suppressed
        }
        return null;
    }

    public static <T> T deserializeFromJson(String json, Class<T> objectClass) {
        try {
            return mapper.readValue(json, objectClass);
        } catch (Exception ex) {
            // suppressed
        }
        return null;
    }

    public static <T> List<T> fromDocumentList(FindIterable<Document> list, Class<T> objectClass) {
        List<T> result = null;
        if (list != null) {
            result = new ArrayList<>();
            for (Document document : list) {
                result.add(fromDocument(document, objectClass));
            }
        }
        return result;
    }
}
