package it.masterzen.Dungeon;

import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.XMaterial;
import it.masterzen.commands.Main;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.sql.Array;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public class GUI implements Listener {

    private it.masterzen.Dungeon.Main main;
    private it.masterzen.Withdraw.Main withdrawManager;
    private final String prefix = "§e§lDUNGEON §8»§7 ";

    public GUI(it.masterzen.Dungeon.Main main) {
        this.main = main;
        withdrawManager = new it.masterzen.Withdraw.Main(main.mainClass);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();
            boolean openMainMenu = false;

            if (event.getView().getTitle().equalsIgnoreCase("§e§lDUNGEON §f| §7Shop")) {
                event.setCancelled(true);
                if (event.getSlot() == 11) {
                    if (event.isLeftClick()) {
                        giveRandomSpawner(player, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveRandomSpawner(player, true);
                        openMainMenu = true;
                    }
                    /*if (event.isLeftClick()) {
                        giveXP(player, false, 5000, 1);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 5000, 1);
                        openMainMenu = true;
                    }*/
                } else if (event.getSlot() == 13) {
                    if (event.isLeftClick()) {
                        giveVillagerSpawner(player, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveVillagerSpawner(player, true);
                        openMainMenu = true;
                    }
                    /*if (event.isLeftClick()) {
                        giveXP(player, false, 5000, 1);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 5000, 1);
                        openMainMenu = true;
                    }*/
                } else if (event.getSlot() == 14) {
                    if (event.isLeftClick()) {
                        giveTokenNoteSpawner(player, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveTokenNoteSpawner(player, true);
                        openMainMenu = true;
                    }
                    /*if (event.isLeftClick()) {
                        giveXP(player, false, 5000, 1);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 5000, 1);
                        openMainMenu = true;
                    }*/
                } else if (event.getSlot() == 15) {
                    if (event.isLeftClick()) {
                        giveMoneyNoteSpawner(player, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveMoneyNoteSpawner(player, true);
                        openMainMenu = true;
                    }
                    /*if (event.isLeftClick()) {
                        giveXP(player, false, 5000, 1);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 5000, 1);
                        openMainMenu = true;
                    }*/
                } else if (event.getSlot() == 16) {
                    if (event.isLeftClick()) {
                        giveSilverfishSpawner(player, false);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveSilverfishSpawner(player, true);
                        openMainMenu = true;
                    }
                    /*if (event.isLeftClick()) {
                        giveXP(player, false, 5000, 1);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 5000, 1);
                        openMainMenu = true;
                    }*/
                } else if (event.getSlot() == 22) {
                    if (player.getInventory().getItemInMainHand() != null && player.getInventory().getItemInMainHand().getType().equals(Material.DIAMOND_PICKAXE)) {
                        openEnchantGUI(player);
                    } else {
                        player.sendMessage(prefix + "§cPlease hold your pickaxe");
                    }
                    /*if (event.isLeftClick()) {
                        giveXP(player, false, 5000, 1);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 5000, 1);
                        openMainMenu = true;
                    }*/
                } /*else if (event.getSlot() == 13) {
                    if (event.isLeftClick()) {
                        giveXP(player, false, 25000, 2);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 25000, 2);
                        openMainMenu = true;
                    }
                } else if (event.getSlot() == 15) {
                    if (event.isLeftClick()) {
                        giveXP(player, false, 100000, 3);
                        openMainMenu = true;
                    } else if (event.isRightClick()) {
                        giveXP(player, true, 100000, 3);
                        openMainMenu = true;
                    }
                } else if (event.getSlot() == 22) {
                    if (player.getInventory().getItemInMainHand() != null && player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                        openEnchantGUI(player);
                    } else {
                        player.sendMessage(prefix + "Please hold the pickaxe to enchant it");
                    }
                }*/

                if (openMainMenu) {
                    openGUI(player);
                }
            }
        }
    }

    public void giveXP(Player player, boolean max, int amount, int tier) {
        long playerBalance = main.getPoints(player, tier);
        long price = 0;
        if (tier == 1) {
            price = 25;
        } else if (tier == 2 || tier == 3) {
            price = 10;
        }

        if (playerBalance >= price || player.isOp()) {
            if (max) {
                long totalXP = 0;
                int iterations = 0;

                while (playerBalance >= price) {
                    totalXP = totalXP + amount; // 50k to 100k
                    playerBalance = playerBalance - price;
                    iterations++;
                }

                withdrawManager.giveItem(player, "XP", totalXP);
                main.removePoints(player, tier, iterations * price);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalXP));
            } else {
                withdrawManager.giveItem(player, "XP", amount);
                main.removePoints(player, tier, price);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(amount));
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough Crystals");
        }
    }

    public void giveRandomSpawner(Player player, boolean maxUse) {
        PlayerData data = AlphaBlockBreak.GetInstance().getMongoReader().getPlayerData(player.getUniqueId());
        int price = 250;
        if (data.getDungeonDiscount() != null) {
            price = 250 - data.getDungeonDiscount();
        }

        if (main.getPoints(player, 1) >= price) {
            int iterations = 1;
            if (maxUse) {
                iterations = 9999;
            }
            Map<String, Integer> spawnersToAdd = new HashMap<>();
            while (main.getPoints(player, 1) >= price && iterations > 0) {
                int chance = ThreadLocalRandom.current().nextInt(4);

                if (chance == 0) {
                    if (spawnersToAdd.containsKey("Silverfish")) {
                        spawnersToAdd.put("Silverfish", spawnersToAdd.get("Silverfish") + 1);
                    } else {
                        spawnersToAdd.put("Silverfish", 1);
                    }
                } else if (chance == 1) {
                    if (spawnersToAdd.containsKey("Villager")) {
                        spawnersToAdd.put("Villager", spawnersToAdd.get("Villager") + 1);
                    } else {
                        spawnersToAdd.put("Villager", 1);
                    }
                } else if (chance == 2) {
                    if (spawnersToAdd.containsKey("Bat")) {
                        spawnersToAdd.put("Bat", spawnersToAdd.get("Bat") + 1);
                    } else {
                        spawnersToAdd.put("Bat", 1);
                    }
                } else {
                    if (spawnersToAdd.containsKey("Parrot")) {
                        spawnersToAdd.put("Parrot", spawnersToAdd.get("Parrot") + 1);
                    } else {
                        spawnersToAdd.put("Parrot", 1);
                    }
                }

                main.removePoints(player, 1, price);
                iterations--;
            }

            for (String spawner : spawnersToAdd.keySet()) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner " + spawner + " " + spawnersToAdd.get(spawner));
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveVillagerSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 3) >= 50) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 3) >= 50 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 3, 50);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Villager " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aVillager §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveMoneyNoteSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 1) >= 100) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 1) >= 100 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 1, 100);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Bat " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aMoney Note §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveTokenNoteSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 2) >= 75) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 2) >= 75 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 2, 75);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Parrot " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aToken Note §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void giveSilverfishSpawner(Player player, boolean maxUse) {
        if (main.getPoints(player, 1) >= 100) {
            int iterations = 1;
            int rewardsToAdd = 0;
            if (maxUse) {
                iterations = 9999;
            }
            while (main.getPoints(player, 1) >= 100 && iterations > 0) {
                rewardsToAdd++;
                main.removePoints(player, 1, 100);
                iterations--;
            }

            if (rewardsToAdd > 0) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "stacker give " + player.getName() + " spawner Silverfish " + rewardsToAdd);
                player.sendMessage(prefix + "You received " + rewardsToAdd + "x §aSilverfish §7Spawner");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough crystals !");
        }
    }

    public void openEnchantGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lENCHANT §f| §7Menu");
        Main.FillBorder(gui);

        List<String> pickaxelore = player.getInventory().getItemInMainHand().getItemMeta().getLore();
        ItemStack fortune = main.mainClass.getEnchantManager().getSlotItem("Dungeon Fortune", pickaxelore, player);
        ItemStack luck = main.mainClass.getEnchantManager().getSlotItem("Dungeon Luck", pickaxelore, player);
        ItemStack killer = main.mainClass.getEnchantManager().getSlotItem("Dungeon Killer", pickaxelore, player);
        ItemStack bomb = main.mainClass.getEnchantManager().getSlotItem("Dungeon Bomb", pickaxelore, player);
        ItemStack luckybeacon = main.mainClass.getEnchantManager().getSlotItem("Lucky Beacon", pickaxelore, player);

        gui.setItem(11, killer);
        gui.setItem(12, fortune);
        gui.setItem(13, luck);
        gui.setItem(14, bomb);
        gui.setItem(15, luckybeacon);

        player.openInventory(gui);
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lDUNGEON §f| §7Shop");
        Main.FillBorder(gui);

        /*ItemStack xpT1 = new ItemStack(Material.EXP_BOTTLE);
        ItemMeta meta = xpT1.getItemMeta();
        meta.setDisplayName("§a5k XP");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§3§lPRICE");
        lore.add("  §b25 §7Tier 1 Crystals");
        lore.add("");
        lore.add("§7Left click: 1x");
        lore.add("§7Right click: Max Use");
        meta.setlore(lore);
        xpT1.setItemMeta(meta);

        ItemStack xpT2 = new ItemStack(Material.EXP_BOTTLE);
        meta = xpT2.getItemMeta();
        meta.setDisplayName("§a25k XP");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§3§lPRICE");
        lore.add("  §b10 §7Tier 2 Crystals");
        lore.add("");
        lore.add("§7Left click: 1x");
        lore.add("§7Right click: Max Use");
        meta.setlore(lore);
        xpT2.setItemMeta(meta);

        ItemStack xpT3 = new ItemStack(Material.EXP_BOTTLE);
        meta = xpT3.getItemMeta();
        meta.setDisplayName("§a100k XP");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§3§lPRICE");
        lore.add("  §b10 §7Tier 3 Crystals");
        lore.add("");
        lore.add("§7Left click: 1x");
        lore.add("§7Right click: Max Use");
        meta.setlore(lore);
        xpT3.setItemMeta(meta);

        ItemStack enchantMenu = new ItemStack(Material.BOOK);
        meta = enchantMenu.getItemMeta();
        meta.setDisplayName("§aDungeon Enchant §7| Menu");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Click me to open the enchant shop");
        meta.setlore(lore);
        enchantMenu.setItemMeta(meta);

        gui.setItem(11, xpT1);
        gui.setItem(13, xpT2);
        gui.setItem(15, xpT3);
        gui.setItem(22, enchantMenu);*/

        PlayerData data = AlphaBlockBreak.GetInstance().getMongoReader().getPlayerData(player.getUniqueId());
        int randomSpawnerPrice = 250;
        int randomSpawnerDiscount = 0;
        if (data.getDungeonDiscount() != null) {
            randomSpawnerPrice = 250 - data.getDungeonDiscount();
            randomSpawnerDiscount = data.getDungeonDiscount();
        }
        ItemStack randomSpawner = XMaterial.SPAWNER.parseItem();
        assert randomSpawner != null;
        ItemMeta meta = randomSpawner.getItemMeta();
        meta.setDisplayName("§6§lRANDOM §f| §eSpawner");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e" + randomSpawnerPrice + " §fTier 1" + (randomSpawnerDiscount > 0 ? " §7§o(-" + randomSpawnerDiscount + ")" : ""));
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 1) + " §fTier 1");
        lore.add("");
        lore.add("§7§lPOSSIBLE REWARDS");
        lore.add("§7| §fSilverfish Spawner");
        lore.add("§7| §fMobCoins Spawner");
        lore.add("§7| §fMoney Note Spawner");
        lore.add("§7| §fToken Note Spawner");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        meta.setLore(lore);
        randomSpawner.setItemMeta(meta);

        ItemStack villagerSpawner = XMaterial.SPAWNER.parseItem();
        assert villagerSpawner != null;
        meta = villagerSpawner.getItemMeta();
        meta.setDisplayName("§6§lMOBCOINS §f| §eSpawner");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e50 §fTier 3");
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 3) + " §fTier 3");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        meta.setLore(lore);
        villagerSpawner.setItemMeta(meta);

        ItemStack moneyNoteSpawner = XMaterial.SPAWNER.parseItem();
        assert moneyNoteSpawner != null;
        meta = moneyNoteSpawner.getItemMeta();
        meta.setDisplayName("§6§lMONEY NOTE §f| §eSpawner");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e100 §fTier 1");
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 1) + " §fTier 1");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        meta.setLore(lore);
        moneyNoteSpawner.setItemMeta(meta);

        ItemStack tokenNoteSpawner = XMaterial.SPAWNER.parseItem();
        assert tokenNoteSpawner != null;
        meta = tokenNoteSpawner.getItemMeta();
        meta.setDisplayName("§6§lTOKEN NOTE §f| §eSpawner");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e75 §fTier 2");
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 2) + " §fTier 2");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        meta.setLore(lore);
        tokenNoteSpawner.setItemMeta(meta);

        ItemStack silverfishSpawner = XMaterial.SPAWNER.parseItem();
        assert silverfishSpawner != null;
        meta = silverfishSpawner.getItemMeta();
        meta.setDisplayName("§6§lSILVERFISH §f| §eSpawner");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e100 §fTier 1");
        lore.add("§e| §fPoints balance: §e" + main.getPoints(player, 1) + " §fTier 1");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft click: 1x");
        lore.add("§6| §fRight click: Max Use");
        lore.add("");
        meta.setLore(lore);
        silverfishSpawner.setItemMeta(meta);

        ItemStack enchantMenu = new ItemStack(Material.BOOK);
        meta = enchantMenu.getItemMeta();
        meta.setDisplayName("§aDungeon Enchant §7| Menu");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Click me to open the enchant shop");
        meta.setLore(lore);
        enchantMenu.setItemMeta(meta);

        gui.setItem(11, randomSpawner);
        gui.setItem(13, villagerSpawner);
        gui.setItem(14, tokenNoteSpawner);
        gui.setItem(15, moneyNoteSpawner);
        gui.setItem(16, silverfishSpawner);
        gui.setItem(22, enchantMenu);

        player.openInventory(gui);
    }
}
