package it.masterzen.blockbreak;

import com.google.common.base.CaseFormat;
import it.masterzen.MongoDB.PlayerData;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class Book implements Listener {

    private final String prefix = "§e§lBOOK §8»§7 ";
    private final int momentumPrice = 5;
    private final int frenzyPrice = 250;

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equals("§e§lREBIRTH §f| §7Shop")) {
                Player player = (Player) event.getWhoClicked();
                event.setCancelled(true);
                AlphaBlockBreak main = AlphaBlockBreak.GetInstance();

                if (event.getSlot() == 13) {
                    if (main.getPlayerRebirth(player.getUniqueId()) >= 500) {
                        if (main.getEmptySlots(player.getInventory()) >= 1) {
                            player.getInventory().addItem(main.getBeaconPickaxe());
                        } else {
                            player.sendMessage(prefix + "§cYou don't have enough space in your inventory");
                        }
                    } else {
                        player.sendMessage(prefix + "§cYou need to be at least Rebirth 500 to purchase the Beacon Pickaxe");
                    }
                } else if (event.getSlot() == 20 || event.getSlot() == 21 || event.getSlot() == 22 || event.getSlot() == 23 || event.getSlot() == 24 || event.getSlot() == 31) {
                    if (!main.isBeaconPickaxe(player)) {
                        int points = main.getPlayerRebirthPoints(player);
                        //Random random = new Random();
                        String enchantName = getEnchantName(event.getCurrentItem().getItemMeta().getDisplayName());
                        EnchantList enchantList = main.getEnchantManager().getEnchantList().get(enchantName);
                        int tierNeeded = enchantList.getColor() - 1;
                        if (enchantList.getColor() == 6) {
                            tierNeeded = 0;
                        }

                        if (enchantName.equalsIgnoreCase("Momentum")) {
                            if (main.getPlayerRebirth(player.getUniqueId()) < 100) {
                                player.sendMessage(prefix + "§cYou need to be at least Rebirth 100");
                                return;
                            }
                        }
                        PlayerData data = main.getMongoReader().getPlayerData(player.getUniqueId());
                        int currentTier = data.getPickaxeCurrentTier() == null ? 0 : data.getPickaxeCurrentTier();
                        if (currentTier >= tierNeeded || player.isOp()) {
                            if (event.getClick().isLeftClick()) {
                                int price = 1;
                                if (!player.isOp()) {
                                    if (enchantName.equalsIgnoreCase("Momentum")) {
                                        price = momentumPrice;
                                    } else if (enchantName.equalsIgnoreCase("Combo")) {
                                        price = 3;
                                    } else if (enchantName.equalsIgnoreCase("Nuke")) {
                                        price = 2;
                                    } else if (enchantName.equalsIgnoreCase("Frenzy")) {
                                        price = frenzyPrice;
                                    } else if (enchantName.equalsIgnoreCase("Robot Finder")) {
                                        price = 5;
                                    }
                                } else {
                                    price = 0;
                                }

                                if ((points >= price || player.isOp()) && player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                                    if (!player.isOp()) {
                                        if (data.getRebirthPointsSpended() == null) {
                                            data.setRebirthPointsSpended(0);
                                        }
                                        data.setRebirthPointsSpended(data.getRebirthPointsSpended() + price);
                                        main.removeRebirthPoints(player, price);
                                    }

                                    /*String enchantName = ChatColor.stripColor(event.getCurrentItem().getItemMeta().getDisplayName().replace(" §7| Level", "")).toLowerCase();
                                    String[] words = enchantName.split(" ");
                                    enchantName = "";

                                    for (String part : words) {
                                        enchantName = enchantName + CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, part) + " ";
                                    }
                                    enchantName = enchantName.replace("greed", "Greed").replace("hammer", "Hammer").trim();*/

                                    int level;
                                    //if (enchantName.equals("Combo")) {
                                    //    level = random.nextInt(2) + 1;
                                    //} else {
                                    if (enchantName.equalsIgnoreCase("Momentum") || enchantName.equalsIgnoreCase("Frenzy")) {
                                        level = 1;
                                    } else if (enchantName.equalsIgnoreCase("Robot Finder")) {
                                        level = ThreadLocalRandom.current().nextInt(5) + 1;
                                    } else {
                                        level = ThreadLocalRandom.current().nextInt(6) + 5;
                                    }
                                    //}

                                    //giveEnchantBook(enchantName, level, player);
                                    main.getEnchantManager().addEnchant(player, enchantName, level, true);
                                    main.openRebirthShop(player);
                                } else if (!player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                                    player.sendMessage(prefix + "§cYou must hold a pickaxe to get the enchants !");
                                } else {
                                    player.sendMessage(prefix + "§cYou don't have enough points !");
                                }
                            } else if (event.getClick().isRightClick()) {
                                int pointsToRemove = 0;
                                int totalLevel = 0;
                                int price = 1;
                                if (!player.isOp()) {
                                    if (enchantName.equalsIgnoreCase("Momentum")) {
                                        price = momentumPrice;
                                    } else if (enchantName.equalsIgnoreCase("Combo")) {
                                        price = 3;
                                    } else if (enchantName.equalsIgnoreCase("Nuke")) {
                                        price = 2;
                                    } else if (enchantName.equalsIgnoreCase("Frenzy")) {
                                        price = frenzyPrice;
                                    } else if (enchantName.equalsIgnoreCase("Robot Finder")) {
                                        price = 5;
                                    }
                                } else {
                                    price = 0;
                                }

                                int maxIterations = 5000;
                                while (points >= price && maxIterations > 0) {
                                    //if (enchantName.equals("Combo")) {
                                    //    totalLevel = totalLevel + random.nextInt(2) + 1;
                                    //} else {
                                    if (enchantName.equalsIgnoreCase("Momentum") || enchantName.equalsIgnoreCase("Frenzy")) {
                                        totalLevel++;
                                    } else if (enchantName.equalsIgnoreCase("Robot Finder")) {
                                        totalLevel = totalLevel + ThreadLocalRandom.current().nextInt(5) + 1;
                                    } else {
                                        totalLevel = totalLevel + ThreadLocalRandom.current().nextInt(6) + 5;
                                    }
                                    //}

                                    points = points - price;
                                    pointsToRemove = pointsToRemove + price;
                                    /*if (enchantName.equalsIgnoreCase("Momentum")) {
                                        pointsToRemove = pointsToRemove + momentumPrice;
                                    } else {
                                        pointsToRemove = pointsToRemove + price;
                                    }
                                    if (enchantName.equalsIgnoreCase("Momentum")) {
                                        points = points - momentumPrice;
                                    } else {
                                        points = points - price;
                                    }*/
                                    maxIterations--;
                                }

                                if (maxIterations == 0) {
                                    main.getLogger().warning("(!) Rebirthshop -> MaxIterations");
                                }

                                if (totalLevel > 0 && player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                                    if (!player.isOp()) {
                                        if (data.getRebirthPointsSpended() == null) {
                                            data.setRebirthPointsSpended(0);
                                        }
                                        data.setRebirthPointsSpended(data.getRebirthPointsSpended() + pointsToRemove);
                                        main.removeRebirthPoints(player, pointsToRemove);
                                    }

                                    //giveEnchantBook(enchantName, totalLevel, player);
                                    main.getEnchantManager().addEnchant(player, enchantName, totalLevel, true);
                                    main.openRebirthShop(player);
                                } else if (!player.getInventory().getItemInMainHand().getType().equals(XMaterial.DIAMOND_PICKAXE.parseMaterial())) {
                                    player.sendMessage(prefix + "§cYou must hold a pickaxe to get the enchants !");
                                } else {
                                    player.sendMessage(prefix + "§cYou don't have enough points !");
                                }
                            }
                        } else {
                            player.sendMessage(prefix + "§cYou need an higher pickaxe tier to apply this enchant");
                        }
                    } else {
                        player.sendMessage(prefix + "§cYou can't enchant this pickaxe");
                    }
                    //main.openRebirthShop(player);
                }
            }
        }
    }

    public String getEnchantName(String name) {
        String enchantName = ChatColor.stripColor(name.replace(" §7| Level", "")).toLowerCase();
        enchantName = enchantName.replace("_", " ");
        String[] words = enchantName.split(" ");
        enchantName = "";

        for (String part : words) {
            enchantName = enchantName + CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, part) + " ";
        }
        enchantName = enchantName.replace("greed", "Greed").replace("hammer", "Hammer").replace("block", "Block").replace("pot", "Pot").replace("land", "Land").replace("Xp", "XP").replace("key", "Key").replace("merchant", "Merchant").replace("ship", "Ship").replace("point", "Point").replace("finder", "Finder").trim();

        return enchantName;
    }

    private String getFixedName(String name) {
        return name.substring(0, 1).toUpperCase() + name.substring(1).toLowerCase();
    }

    public String getBookDisplayName(String name) {
        return "§6§l" + name.toUpperCase() + " §7| Level";
    }

    public List<String> getEnchantLore(String name, int level) {
        List<String> Lore = new ArrayList<>();
        Lore.add("");
        Lore.add("§7Drag and drop in a pickaxe");
        Lore.add("§7to add §a§l" + level + " §7level of " + getFixedName(name));
        Lore.add("");

        return Lore;
    }

    public List<String> getRebirthEnchantLore(String name) {
        List<String> Lore = new ArrayList<>();

        int price = 1;
        if (name.equalsIgnoreCase("Momentum")) {
            price = momentumPrice;
        } else if (name.equalsIgnoreCase("Combo")) {
            price = 3;
        } else if (name.equalsIgnoreCase("Nuke")) {
            price = 2;
        } else if (name.equalsIgnoreCase("Frenzy")) {
            price = frenzyPrice;
        } else if (name.equalsIgnoreCase("Robot Finder")) {
            price = 5;
        }

        if (name.equalsIgnoreCase("Momentum") || name.equalsIgnoreCase("Frenzy")) {
            Lore.add("§c§l* UNIQUE *");
        }
        Lore.add("");
        Lore.add("§e§lPRICE");
        Lore.add("§e| §e" + price + " §fRebirth Points");
        Lore.add("");
        if (name.equalsIgnoreCase("Momentum")) {
            Lore.add("§7§lDESCRIPTION");
            Lore.add("§7| §fYou will receive 1 level");
            Lore.add("§7| §fof §7" + name);
            Lore.add("");
            Lore.add("§7| §fWhile mining, every 3 seconds, you will receive");
            Lore.add("§7| §fan extra +1% Money and Tokens");
            Lore.add("§7| §fThe limit is based on your Momentum level");
            Lore.add("");
            Lore.add("§a§lREQUIREMENT");
            Lore.add("§a| §fRebirth §a100");
            Lore.add("");
        } else if (name.equalsIgnoreCase("Frenzy")) {
            Lore.add("§7§lDESCRIPTION");
            Lore.add("§7| §fYou will receive 1 level");
            Lore.add("§7| §fof §7" + name);
            Lore.add("");
            Lore.add("§7| §fThis enchant will increase your chance");
            Lore.add("§7| §fto get Armor and Alpha keys from KeyFinder");
            Lore.add("");
        } else if (name.equalsIgnoreCase("Robot Finder")) {
            Lore.add("§7§lDESCRIPTION");
            Lore.add("§7| §fYou will receive a random");
            Lore.add("§7| §famount of levels of §7" + name/*getFixedName(name)*/);
            Lore.add("§7| §ffrom §71 §fto §75");
            Lore.add("");
        } else {
            Lore.add("§7§lDESCRIPTION");
            Lore.add("§7| §fYou will receive a random");
            Lore.add("§7| §famount of levels of §7" + name/*getFixedName(name)*/);
            Lore.add("§7| §ffrom §75 §fto §710");
            Lore.add("");
        }
        //Lore.add("");
        //Lore.add("§7§o( Drag and drop to add )");
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft Click: §61x");
        Lore.add("§6| §fRight click: §6Max Use");
        Lore.add("");

        return Lore;
    }

    public void giveEnchantBook(String name, int level, Player player) {
        List<String> Lore = new ArrayList<>();
        ItemStack book = XMaterial.BOOK.parseItem();
        assert book != null;
        ItemMeta meta = book.getItemMeta();
        meta.setDisplayName("§6§l" + name.toUpperCase() + " §7| Level");
        Lore.add("");
        Lore.add("§7Drag and drop in a pickaxe");
        Lore.add("§7to add §a§l" + level + " §7level of §a§l" + name);
        Lore.add("");
        meta.setLore(Lore);
        book.setItemMeta(meta);
        player.getInventory().addItem(book);
        player.sendMessage(prefix + "You succesfully received 1 book of §a§l" + name + "");
    }

}
